import { RDSCard } from "@roshn/ui-kit";

export const UnitCard = ({ unitDetails }: any) => {
  const { propertyType, price, status, images, features } = unitDetails;
  console.log(propertyType, price, status, images, features, "unitDetails");
  const cardData = {
    key: 1,
    media: "onTop",
    mediaPadding: false,
    contentPadding: false,
    cardContentProps: {
      direction: "horizontal",
      topBar: false,
      bgPattern: false,
      mediaCarouselProps: {
        aspectRatio: "1 / 1",
        images: images,
        tags: [{ label: status, type: "success" }],
        carouselCaret: false,
        bottomNavigation: true,
      },
      bottomBar: true,
      bottomBarProps: {
        propertyHeader: {
          type: "propertyHeader",
          label: `SAR ${price}`,
          nestedContent: false,
          cardContentSameLevel: {
            type: "nestedLabel",
            label: propertyType,
            divider: false,
          },
        },
        propertyDescription: [
          {
            type: "propertyFeatures",
            nestedContent: true,
            label: propertyType,
            cardContentPropertyLabel: {
              type: "label",
              label: "Features",
              divider: false,
            },
            cardContentPropertyNested: [...features],
          },
        ],
      },
    },
  };
  console.log(cardData, "cardData");
  return <RDSCard {...cardData} />;
};
