import { css, useTheme } from "@emotion/react";
import { data, useNavigate } from "@remix-run/react";
import {
  RDSTable,
  RDSTagInteractive,
  RDSButton,
  RDSTypography,
  AppTheme,
  RDSSearchInput,
  RDSModal,
  RDSUploadFile,
  RDSAssetWrapper,
} from "@roshn/ui-kit";
import { useEffect, useMemo, useState } from "react";

import { createSvg } from "~/components/svgs";
import { RoshnContainerLoader } from "~/features/common/loading/roshn-container-loader";
import { useAppPath } from "~/hooks/use-app-path";
import { QueryProductListParams, useProductList } from "~/hooks/use-product-list";
import { AppPaths } from "~/utils/app-paths";
import { UnitCard } from "./unit-card";

const Upload = createSvg(() => import("~/assets/icons/upload.svg"));

const tagData = [
  { label: "All", state: "active" },
  { label: "Hidden", state: "default" },
  { label: "Visible", state: "default" },
];

const featuresMap = ["NumberOfBedrooms", "NumberOfBathrooms", "NumberOfKitchens", "LandAreaSQM"]

const styles = {
  wrapper: (theme: AppTheme) =>
    css({
      background: theme?.rds?.color.background.brand.secondary.inverse.default,
      minHeight: "100vh",
      width: "100%",
    }),
  header: (theme: AppTheme) =>
    css({
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      paddingBottom: theme?.rds?.dimension["200"],
    }),
  ctaContainer: (theme: AppTheme) =>
    css({
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      gap: theme?.rds?.dimension["200"],
    }),
  searchInput: (theme: AppTheme) =>
    css({
      gap: theme.rds.dimension["200"],
    }),
  tagContainer: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["200"],
      marginTop: theme.rds.dimension["300"],
    }),

  modalDimension: css({
    "& div": {
      "& div": {
        maxWidth: "640px",
      },
    },
  }),
  cardContainer: css({
    display: "grid",
    gridGap: "1rem",
    gridTemplateColumns: "repeat(auto-fill, minmax(300px, auto))",
    justifyContent: "space-between",
    width: "100%",
  }),
};

const AddUnits = ({ onSuccess }: { onSuccess: () => void }) => {
  const [uploadModal, setUploadModal] = useState(false);
  const [inpFile, setInpFile] = useState<unknown>();
  const [loading, setLoading] = useState(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    setInpFile(file);
    document.getElementsByClassName("roshn-boilerplate-fe-1jke4yk")[0].remove();
    if (!file) return;
  };

  const handelCloseModal = () => {
    setUploadModal(false);
    setInpFile(null);
  };

  useEffect(() => {
    if (loading) {
      const timer = setTimeout(() => {
        setLoading(false);
        handelCloseModal();
        onSuccess();
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [loading]);

  return (
    <>
      <RDSButton
        css={{ textTransform: "none" }}
        variant="secondary"
        size="lg"
        text="IMPORT UNITS"
        data-testid="upload-button"
        onClick={() => setUploadModal(true)}
        leadIcon={
          <RDSAssetWrapper>
            <Upload />
          </RDSAssetWrapper>
        }
      />
      <div css={styles.modalDimension}>
        <RDSModal
          headerProps={{
            label: "Upload File",
            type: "centred",
          }}
          showContent
          showDescription
          description="Add multiple units in bulk by uploading a CSV file. Ensure your file follows the required format to correctly import property details like unit id, status, project and price availability."
          isOpen={uploadModal}
          content={
            <RDSUploadFile
              onChange={(e) => handleFileChange(e)}
              onDismissHandler={() => setInpFile(null)}
              accept=".csv"
            />
          }
          footer
          buttonsGroup={{
            buttons: [
              <RDSButton
                variant="primary"
                onClick={() => setLoading(true)}
                text="SUBMIT FILES"
                disabled={!inpFile}
                loading={loading}
                key="enrollment"
              />,
              <RDSButton
                variant="secondary"
                onClick={handelCloseModal}
                text="BACK TO INVENTORY"
                key="continue"
              />,
            ],
            direction: "vertical",
          }}
        />
      </div>
    </>
  );
};

export default function UnitListSection() {
  const theme = useTheme() as AppTheme;
  const [activeTag, setActiveTag] = useState("All");
  const [search, setSearch] = useState({ searchQuery: "" });
  const [searchParam, setSearchParam] = useState({ search: "" });
  const generatePath = useAppPath();
  const navigate = useNavigate();

  const productListParams: QueryProductListParams = {
    marketplace_product_status: activeTag === "All" ? "" : "APPROVED",
    search: searchParam.search,
  };

  const { data, isFetching, isSuccess } = useProductList(productListParams);

  const [productList, setProductList] = useState<unknown>([]);

  const responseUnitData = useMemo(() => {
    const unitResults = data?.results.map((data) => {
      return {
        propertyType: data?.description,
        price: data?.price,
        status: data?.marketplace_product?.status_display,
        images: data?.marketplace_categories.map((marketplace) => marketplace.image),
        features: data.custom_attributes.map(({ label, value }: { label: string; value: string }, index: number) => {
          if (!featuresMap.includes(label)) return {};
          return { 
              type: "nestedLabel",
              label: `${label} ${value}`,
              divider: index !== data.custom_attributes.length - 1,
             };
        }).filter((unit) => Object.keys(unit).length > 0),
      };
    });  
    console.log(unitResults, "unitResults");
    return unitResults;
  }, [data]);

  console.log(responseUnitData, "responseUnitData");

  useEffect(() => {
    if (isSuccess) {
      setProductList(data?.results || []);
    }
  }, [isSuccess]);

  return (
    <div css={styles.wrapper(theme)}>
      <div css={styles.header(theme)}>
        <div css={{ ...theme?.rds?.typographies?.heading?.emphasis?.h4 }}>Listed units ({data?.count})</div>
        <div css={styles.ctaContainer}>
          <AddUnits
            onSuccess={() => {
              setProductList((prev) => [...data?.results, ...prev]);
            }}
          />
          <RDSButton
            size="lg"
            text="+ ADD UNIT"
            onClick={() => navigate(generatePath(AppPaths.addUnit))}
          />
        </div>
      </div>
      <div style={{ marginBottom: theme.rds.dimension["400"] }}>
        <RDSSearchInput
          type="text"
          placeholder="Search by name, city, status..."
          css={styles.searchInput(theme)}
          onChange={(e) => {
            setSearch({ searchQuery: e.target.value });
          }}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              setSearchParam({ search: search.searchQuery });
            }
          }}
        />
        <div css={styles.tagContainer(theme)}>
          {tagData.map((tag) => (
            <RDSTagInteractive
              key={tag.label}
              size="md"
              label={tag.label}
              state={tag.label === activeTag ? "active" : "default"}
              // onClick={() => setActiveTag(tag.label)}
            />
          ))}
        </div>
      </div>
      {isFetching ? <RoshnContainerLoader /> : (
        <div css={styles.cardContainer}>
          {responseUnitData.map((unitData) => <UnitCard unitDetails={unitData} />)}
        </div>
      )}
    </div>
  );
}
