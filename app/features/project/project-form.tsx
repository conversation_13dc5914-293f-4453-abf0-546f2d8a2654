import { css, useTheme } from "@emotion/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useNavigate } from "@remix-run/react";
import {
  AppTheme,
  RDSAssetWrapper,
  RDSButton,
  RDSEmptyState,
  RDSModal,
  RDSProgressSteps,
  RDSTable,
  RDSTextInput,
  RDSTypography,
} from "@roshn/ui-kit";
import isEmpty from "lodash/isEmpty";
import { useEffect, useMemo, useState } from "react";
import { FieldErrors, FieldValues, Form, useForm } from "react-hook-form";
import { z } from "zod";

import { FieldRenderer } from "~/components/field-renderer/field-renderer";
import { UploadFile } from "~/components/form-components/file-upload/file-upload";
import { Input } from "~/components/form-components/input/input";
import { Section } from "~/components/section/section";
import { createSvg } from "~/components/svgs";
import { useAppPath } from "~/hooks/use-app-path";
import { AppPaths } from "~/utils/app-paths";

import { fieldConfig } from "./field-config";

const Check = createSvg(() => import("~/assets/icons/check.svg"));

type StepState = "active" | "complete" | "in-complete";

type StepId = "draft" | "in-review" | "approved";

type Step = {
  stepTitle: string;
  state: StepState;
  id: StepId;
};

const styles = {
  wrapper: (theme: AppTheme) =>
    css({
      paddingInline: theme.rds.dimension["800"],
      paddingBlock: theme.rds.dimension["400"],
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
      alignItems: "flex-start",
      backgroundColor: theme.rds.color.background.ui.secondary.default,
      minHeight: "100vh",
    }),

  button: (theme: AppTheme) =>
    css({
      textTransform: "none",
      width: "fit-content",
      paddingInlineStart: 0,

      "& svg": {
        color: `${theme.rds.color.text.brand.primary.default} !important`,
      },
    }),

  sectionWrapper: (theme: AppTheme) =>
    css({
      width: "100%",
      backgroundColor: theme.rds.color.background.ui.canvas,
      padding: theme.rds.dimension["300"],
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      border: `1px solid ${theme.rds.color.border.ui.primary}`,
    }),

  sectionHeadingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h5,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary,
      textTransform: "uppercase",
    }),

  actionsHeadingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h5,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary,
    }),

  infoHead: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.lg,
      color: theme.rds.color.text.ui.tertiary,
    }),

  infoDes: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.emphasis.lg,
      color: theme.rds.color.text.ui.primary,
    }),

  internalWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      zIndex: 0,
    }),

  sectionChildrenWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
    }),

  sectionDivider: (theme: AppTheme) =>
    css({
      height: "1px",
      backgroundColor: theme.rds.color.border.ui.primary,
    }),

  form: css({
    flex: "0 0 70%",
  }),

  sectionsWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["200"],
      height: "fit-content",
      width: "100%",
    }),

  infoTypoWrapper: css({
    display: "flex",
    justifyContent: "space-between",
  }),

  sectionLayout: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
    }),

  infoSections: (theme: AppTheme) =>
    css({
      flex: "0 0 30%",
      position: "sticky",
      top: theme.rds.dimension["400"],
      alignSelf: "flex-start",
    }),

  actionsLayout: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["100"],
    }),

  listingTypo: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.sm,
      color: theme.rds.color.text.ui.tertiary,
      height: "fit-content",
    }),

  modalDimension: css({
    "& div": {
      "& div": {
        maxWidth: "640px",
      },
    },
    "& p": {
      margin: 0,
    },
  }),

  radio: (theme: AppTheme) =>
    css({
      "& div": {
        color: theme.rds.color.text.ui.primary,
        position: "relative",
      },
      "& .roshn-boilerplate-fe-18twejl::after": {
        fontSize: "1.1rem",
        content: '" *"',
        color: theme?.rds?.color.text.functional.danger.tertiary,
      },
    }),

  sectionHeadingWrapper: css({
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
  }),

  dangerBtn: (theme: AppTheme) =>
    css({
      backgroundColor: theme?.rds?.color.background.functional.danger.darker.default,
      borderColor: theme?.rds?.color.border.functional.danger.darker.default,
      color: theme?.rds?.color.text.brand.primary.onPrimary,
      cursor: "pointer",
      "&:hover": {
        backgroundColor: theme?.rds?.color.background.functional.danger.hover,
        color: theme?.rds?.color.text.functional.danger.onDanger,
        transition: "300ms ease-in-out",
      },
      "&:active": {
        backgroundColor: theme?.rds?.color.background.functional.danger.pressed,
        color: theme?.rds?.color.text.functional.danger.onDanger,
        transition: "300ms ease-in-out",
      },
      "&[data-disabled=true]": {
        cursor: "not-allowed",
        outline: `1px solid ${theme?.rds?.color.border.ui.disabled}`,
        color: theme?.rds?.color.icon.ui.onDisabled,
        backgroundColor: theme?.rds?.color.background.ui.disabled,
      },
    }),

  tableWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      border: `1px solid ${theme.rds.color.border.ui.primary}`,
    }),

  header: (theme: AppTheme) =>
    css({
      paddingInline: "16px",
      paddingBlock: "8px",
      height: theme?.rds?.dimension["500"],
      alignItems: "center",
      display: "flex",
      backgroundColor: theme.rds.color.background.ui.tertiary.default,

      "& > span": {
        ...theme?.rds?.typographies?.label.emphasis.md,
        color: theme.rds.color.text.ui.primary,
        fontWeight: 500,
        width: "50%",
        textAlign: "left",
      },
    }),

  row: (theme: AppTheme, isLast: boolean) =>
    css({
      color: theme.rds.color.text.ui.primary,
      fontWeight: 500,
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      paddingInline: "16px",
      paddingBlock: "8px",
      borderBottom: isLast ? "none" : "1px solid #eee",
      backgroundColor: "#fff",
      height: "56px",

      "& > div": {
        width: "50%",
        textAlign: "left",
      },
    }),

  inputWrapper: (theme: AppTheme) =>
    css({
      ...theme?.rds?.typographies?.label.emphasis.md,
      display: "flex",
      alignItems: "center",
    }),

  input: (theme: AppTheme) =>
    css({
      width: "100%",
      padding: theme.rds.dimension["200"],
      textAlign: "right",
    }),

  bottomInpWrapper: css({
    width: "50%",
    alignSelf: "end",
  }),
};

const schema = z.object({
  logo: z.any(),
  nameEn: z.string().min(1, "English name is required"),
  nameAR: z.string().min(1, "Arabic name is required"),
  city: z.string().min(1, "City is required"),
  licenseNumber: z.string().min(1, "License number is required"),
  handOverDate: z.date(),
  description: z.string().min(1, "Description is required"),
  amenities: z.array(z.string()).min(1, "At least one amenity is required"),
  projectDocuments: z.array(z.instanceof(File)).min(1, "At least one document is required"),
  refundable: z.enum(["yes", "no"], {
    errorMap: () => ({ message: "Refundable is required" }),
  }),
  feeType: z.string().min(1, "Fee type is required"),
  reservationFeeAmount: z.string().min(1, "Reservation fee amount is required"),
});

const tableSchema = z.object({
  downPayment: z.any(),
  construction: z.any(),
  handover: z.any(),
});

type ProjectFormValues = z.infer<typeof schema>;

export default function ProjectForm({
  defaultValues = null,
}: {
  defaultValues?: ProjectFormValues;
}) {
  const navigate = useNavigate();
  const generateAppPath = useAppPath();
  const theme = useTheme() as AppTheme;

  const [showReviewModal, setReviewModal] = useState(false);
  const [showSubmitModal, setSubmitModal] = useState(false);
  const [showWarningModal, setWarningModal] = useState(false);

  const [steps, setSteps] = useState<Step[]>([
    { stepTitle: "Draft", state: "active", id: "draft" },
    { stepTitle: "In review", state: "in-complete", id: "in-review" },
    { stepTitle: "Approved", state: "in-complete", id: "approved" },
  ]);

  const {
    handleSubmit,
    control,
    formState: { isValid, errors, touchedFields },
    watch: war,
    reset,
  } = useForm({
    resolver: zodResolver(schema),
    mode: "onChange",
    defaultValues,
  });

  const handleProjectNav = () => {
    navigate(generateAppPath(AppPaths.project));
  };

  const handlePageTransition = () => {
    setWarningModal(true);
  };

  const handleProjectDetailNav = () => {
    navigate(generateAppPath(AppPaths.projectDetail));
  };

  const {
    control: tableControl,
    watch,
    reset: tableReset,
  } = useForm({
    resolver: zodResolver(tableSchema),
    mode: "onChange",
    defaultValues: {
      construction: 0,
      downPayment: 0,
      handover: 0,
    },
  });

  const [dummyLoad, setDummyLoad] = useState(false);

  useEffect(() => {
    if (dummyLoad) {
      const timer = setTimeout(() => {
        setDummyLoad(false);
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [dummyLoad]);

  const onDiscard = () => {
    setUploadKey((prev) => prev + 1); // force re-render

    const resetValues = {
      logo: null,
      nameEn: "",
      nameAR: "",
      city: "",
      licenseNumber: "",
      handOverDate: null,
      description: "",
      amenities: [],
      projectDocuments: [],
      refundable: "",
      feeType: "",
      reservationFeeAmount: "",
    };

    if (defaultValues) {
      reset(defaultValues);
      tableReset({
        construction: 0,
        downPayment: 0,
        handover: 0,
      });
    } else {
      reset(resetValues);
      tableReset({
        construction: 0,
        downPayment: 0,
        handover: 0,
      });
    }
  };

  const getFormStatusMessage = <T extends FieldValues>(
    errors: FieldErrors<T>,
    touchedFields: Partial<Record<keyof T, boolean>>,
  ): string => {
    const hasErrors = !isEmpty(errors);
    const hasTouched = !isEmpty(touchedFields);

    if (hasErrors) return "Fix the errors marked in the form before saving.";
    if (hasTouched) return "You have unsaved changes.";
    return "No changes to be saved.";
  };

  const fieldsError = !isEmpty(errors);

  const watchedValues = watch(["downPayment", "construction", "handover"]);

  const sum = useMemo(() => {
    const [downPayment, construction, handover] = watchedValues.map((v) =>
      isNaN(v) ? 0 : Number(v),
    );
    return downPayment + construction + handover;
  }, [watchedValues]);

  const { projectDetails, additionalDetails, paymentPlan, projectDocuments, calculator } =
    fieldConfig;

  const [uploadKey, setUploadKey] = useState(0);

  return (
    <>
      <div css={styles.modalDimension}>
        <RDSModal
          headerProps={{
            label: "Ready to submit?",
            type: "centred",
          }}
          isOpen={showReviewModal}
          buttonsGroup={{
            buttons: [
              <RDSButton
                variant="primary"
                onClick={() => {
                  setReviewModal(false);
                  setSubmitModal(true);
                }}
                text="SUBMIT FOR REVIEW"
                key="enrollment"
              />,
              <RDSButton
                variant="secondary"
                onClick={() => setReviewModal(false)}
                text="KEEP EDITING"
                key="continue"
              />,
            ],
            direction: "vertical",
          }}
          description="Once submitted, you won’t be able to edit your project while our team reviews it for approval. We’ll notify you within 2 business days about the outcome."
          showContent
          showDescription
        />
        <RDSModal
          isOpen={showSubmitModal}
          showContent
          content={
            <RDSEmptyState
              title="Project submitted!"
              appearance="success"
              size="sm"
              description="Your project is now under review. We’ll notify you as soon as there’s an update. In the meantime, you can check your project details or return to all projects."
              buttons={[
                {
                  text: "VIEW PROJECT DETAILS",
                  variant: "primary",
                  onClick: () => {
                    handleProjectDetailNav();
                    setSubmitModal(false);
                  },
                },
                {
                  text: "BACK TO PROJECTS",
                  variant: "secondary",
                  onClick: () => {
                    handleProjectNav();
                    setSubmitModal(false);
                  },
                },
              ]}
            />
          }
        />
        <RDSModal
          isOpen={showWarningModal}
          showContent
          content={
            <RDSEmptyState
              title="Leave without saving?"
              size="sm"
              description="You have unsaved changes. If you leave now, your updates will be lost. Are you sure you want to exit?"
              buttons={[
                {
                  css: styles.dangerBtn,
                  text: "DISCARD CHANGES AND EXIT",
                  onClick: () => {
                    handleProjectNav();
                  },
                },
                {
                  text: "STAY AND KEEP EDITING",
                  variant: "secondary",
                  onClick: () => {
                    setWarningModal(false);
                  },
                },
              ]}
              showMedia={false}
            />
          }
        />
      </div>
      <div css={styles.wrapper}>
        <RDSButton
          css={styles.button}
          variant="tertiary"
          size="lg"
          text="Back to projects"
          leadIcon="left_arrow"
          onClick={handlePageTransition}
        />
        <div css={styles.sectionsWrapper}>
          <Form css={[styles.form, styles.sectionLayout]} control={control}>
            <Section heading="PROJECT DETAILS">
              <UploadFile
                key={uploadKey}
                control={control}
                label="Project logo"
                name="logo"
                caption="JPG or PNG less than 5MB"
              />
              {projectDetails.map((field) => (
                <FieldRenderer key={field.name} control={control} {...field} />
              ))}
            </Section>
            <Section heading="Additional Details">
              {additionalDetails.map((field) => (
                <FieldRenderer key={field.name} control={control} {...field} />
              ))}
            </Section>

            <Section heading="Attachments">
              {projectDocuments.map((field) => (
                <FieldRenderer key={field.name} control={control} {...field} />
              ))}
            </Section>

            <Section heading="Payment Plan">
              {paymentPlan.map((field) => (
                <FieldRenderer key={field.name} control={control} {...field} />
              ))}
              <RDSTypography
                css={{
                  ...theme?.rds?.typographies?.body?.emphasis?.md,
                  fontWeight: 500,
                  color: theme?.rds?.color?.text?.ui?.primary,
                }}
              >
                Payment schedule:
              </RDSTypography>

              <div css={styles.tableWrapper}>
                <div css={styles.header}>
                  <span>Payment type</span>
                  <span>Amount to pay per installment</span>
                </div>

                {calculator.map(({ label, name, attribute_type }, idx) => (
                  <div key={label} css={styles.row(theme, idx === calculator.length - 1)}>
                    {label && <span>{label}</span>}
                    <div css={styles.inputWrapper}>
                      <FieldRenderer
                        control={tableControl}
                        name={name}
                        attribute_type={attribute_type}
                        suffixText="%"
                      />
                    </div>
                  </div>
                ))}
              </div>
              <div css={styles.bottomInpWrapper}>
                <RDSTextInput
                  suffixText="%"
                  type="text"
                  disabled
                  label="Total amount to be paid"
                  helperText="This number needs to be 100."
                  value={sum}
                />
              </div>
            </Section>
          </Form>
          <div css={[styles.sectionLayout, styles.infoSections]}>
            <Section heading="Actions">
              <div css={styles.internalWrapper}>
                <RDSTypography
                  css={[
                    styles.actionsHeadingText,
                    fieldsError && { color: theme.rds.color.text.functional.danger.tertiary },
                  ]}
                >
                  {getFormStatusMessage(errors, touchedFields)}
                </RDSTypography>
                <RDSButton
                  variant="primary"
                  loading={dummyLoad}
                  ss
                  onClick={() => setDummyLoad(true)}
                  size="lg"
                  text="Save Changes"
                  disabled={!isValid}
                />
                <RDSButton
                  variant="secondary"
                  size="lg"
                  onClick={() => onDiscard()}
                  text="Discard Changes"
                />
              </div>
            </Section>

            <Section heading="LISTING STATUS" tag={{ label: "Draft", appearance: "info" }}>
              <div css={styles.internalWrapper}>
                <RDSProgressSteps type="number" steps={steps} size="md" />
                <RDSTypography css={styles.actionsHeadingText}>
                  Your project is not visible to customers. Please complete all required fields
                  before submitting for review.
                </RDSTypography>
                <RDSButton
                  variant="primary"
                  size="lg"
                  text="SUBMIT FOR REVIEW"
                  leadIcon={
                    <RDSAssetWrapper>
                      <Check />
                    </RDSAssetWrapper>
                  }
                  disabled={!isValid}
                  onClick={() => setReviewModal(true)}
                />
              </div>
            </Section>

            <Section heading="Information">
              <div css={styles.actionsLayout}>
                <div css={styles.infoTypoWrapper}>
                  <RDSTypography css={styles.infoHead}>Created on</RDSTypography>
                  <RDSTypography css={styles.infoDes}>-</RDSTypography>
                </div>
                <div css={styles.infoTypoWrapper}>
                  <RDSTypography css={styles.infoHead}>Created by</RDSTypography>
                  <RDSTypography css={styles.infoDes}>-</RDSTypography>
                </div>
                <div css={styles.infoTypoWrapper}>
                  <RDSTypography css={styles.infoHead}>Updated on</RDSTypography>
                  <RDSTypography css={styles.infoDes}>-</RDSTypography>
                </div>
                <div css={styles.infoTypoWrapper}>
                  <RDSTypography css={styles.infoHead}>Updated by</RDSTypography>
                  <RDSTypography css={styles.infoDes}>-</RDSTypography>
                </div>
              </div>
            </Section>
          </div>
        </div>
      </div>
    </>
  );
}
