import { css } from "@emotion/react";
import { AppTheme, RDSTypography } from "@roshn/ui-kit";

import { createSvg } from "~/components/svgs";

const Tree = createSvg(() => import("~/assets/icons/tree.svg"));
const Mosque = createSvg(() => import("~/assets/icons/mekka.svg"));
const Hospital = createSvg(() => import("~/assets/icons/hospital-building.svg"));
const Graduate = createSvg(() => import("~/assets/icons/graduate.svg"));

const styles = {
  listingTypo: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.sm,
      color: theme.rds.color.text.ui.tertiary,
      height: "fit-content",
    }),
};

const tagOptions = [
  {
    label: "Public Park",
    value: "park",
    disabled: false,
    leadIcon: <Tree />,
  },
  {
    label: "Mosque",
    value: "mosque",
    disabled: false,
    leadIcon: <Mosque />,
  },
  {
    label: "Health center",
    value: "health-center",
    disabled: false,
    leadIcon: <Hospital />,
  },
  {
    label: "Dinning & entertainment",
    value: "dinning-and-entertainment",
    disabled: false,
    leadIcon: <Tree />,
  },
  {
    label: "Sports ground",
    value: "sport-ground",
    disabled: false,
    leadIcon: <Tree />,
  },
  {
    label: "School",
    value: "school",
    disabled: false,
    leadIcon: <Graduate />,
  },
  {
    label: "Kindergarten",
    value: "kindergarten",
    disabled: false,
    leadIcon: <Tree />,
  },
  {
    label: "Retail center",
    value: "retail-center",
    disabled: false,
    leadIcon: <Tree />,
  },
];

export const fieldConfig = {
  projectDetails: [
    {
      attribute_type: "TEXT",
      name: "nameEn",
      label: "Project name (English)",
      placeholder: "Example inc...",
      isRequired: true,
      helperText: "This name will be visible in the english version of your project page.",
    },
    {
      attribute_type: "TEXT",
      name: "nameAR",
      label: "اسم المشروع (بالعربية)",
      placeholder: "مثال: مشروع الإبتكار العقاري...",
      isRequired: true,
      helperText: "سيظهر هذا الاسم في النسخة العربية من صفحة مشروعك.",
      dir: "rtl",
    },
    {
      attribute_type: "TEXT",
      name: "city",
      label: "City",
      placeholder: "Select a city...",
      isRequired: true,
      helperText: "Specify the city where the project is located.",
    },
    {
      attribute_type: "TEXT",
      name: "licenseNumber",
      label: "REGA license number",
      placeholder: "123...",
      isRequired: true,
      helperText: "Your official project registration number.",
    },
    {
      attribute_type: "DATE",
      name: "handOverDate",
      placeholderText: "Select a date...",
      startDate: "new Date()",
      dateInputProps: {
        label: "Handover date",
        isRequired: true,
        helperText: "The date when the project will be delivered to the client.",
      },
    },
    {
      attribute_type: "EDITOR",
      name: "description",
      label: "Description",
      placeholder:
        "Tell us more about your project (max 500 characters). Use formatting to highlight features.",
      isRequired: true,
      helperText: "This description will be visible in the english version of your project page.",
    },
  ],
  additionalDetails: [
    {
      attribute_type: "MULTI_SELECT",
      name: "amenities",
      label: "Nearby amenities ",
      description: "Select available amenities in the community.",
      options: tagOptions,
    },
  ],
  attachments: [
    {
      attribute_type: "IMAGE",
      name: "projectDocuments",
      label: "Project documents",
      description: "Upload all relevant project documents.",
    },
  ],
  paymentPlan: [
    {
      attribute_type: "BOOLEAN",
      name: "refundable",
      label: "Reservation fee:",
      caption: "Refundable?",
      options: [
        { label: "Yes", value: "yes" },
        { label: "No", value: "no" },
      ],
    },
    {
      attribute_type: "BOOLEAN",
      name: "feeType",
      label: "Reservation fee:",
      caption: "Fee Type",
      options: [
        { value: "fixed-amount", label: "Fixed amount" },
        { value: "percentage-total", label: "Percentage of total" },
      ],
    },
    {
      attribute_type: "NUMBER",
      type: "number",
      name: "reservationFeeAmount",
      label: "Reservation fee amount (SAR)",
      leadIcon: <RDSTypography css={styles.listingTypo}>SAR</RDSTypography>,
      isRequired: true,
      placeholder: "Enter price...",
      helperText:
        "Enter the full amount to be paid for the reservation. This will be used in the payment plan.",
    },
  ],
  projectDocuments: [
    {
      attribute_type: "UPLOAD",
      name: "projectDocuments",
      label: "Upload File",
    },
  ],

  calculator: [
    {
      label: "Down payment",
      name: "construction",
      attribute_type: "NUMBER",
    },
    {
      label: "During construction",
      name: "downPayment",
      attribute_type: "NUMBER",
    },
    {
      label: "On handover",
      name: "handover",
      attribute_type: "NUMBER",
    },
  ],
};
