import AddProductPage from "~/pages/add-product/add-product";

const formSchema = {
    id: 1,
    name: "Real Estate - Off-plan",
    name_en: "Real Estate - Off-plan",
    name_ar: "وحدات عقارية على الخارطة",
    slug: "real-estate-off-plan",
    template_id: "real_estate_off_plan_v1",
    version: 1,
    description: "Real Estate - Off-plan Projects",
    description_en: "Real Estate - Off-plan Projects",
    description_ar: "Real Estate - Off-plan Projects",
    is_active: true,
    order: 0,
    marketplace: 1,
    marketplace_merchant: null,
    product_attributes: [
      {
        id: 14,
        name: "unitCode",
        slug: "unitcode",
        attribute_type: "TEXT",
        options: [],
        is_required: true,
        is_translatable: false,
        order: 0,
        value: "12345"
      },
      {
        id: 15,
        name: "propertyType",
        slug: "propertytype",
        attribute_type: "SELECT",
        options: ["Townhouse", "Villa", "Apartment"],
        is_required: true,
        is_translatable: false,
        order: 1,
      },
      {
        id: 16,
        name: "numberofBedrooms",
        slug: "numberofbedrooms",
        attribute_type: "COUNTER",
        options: [],
        is_required: true,
        is_translatable: false,
        order: 2,
      },
      {
        id: 20,
        name: "grossFloorAreaSqm",
        slug: "grossfloorareasqm",
        attribute_type: "NUMBER",
        options: [],
        is_required: false,
        is_translatable: false,
        order: 3,
      },
      {
        id: 17,
        name: "numberofBathrooms",
        slug: "numberofbathrooms",
        attribute_type: "COUNTER",
        options: [],
        is_required: true,
        is_translatable: false,
        order: 4,
      },
    ],
    category_attributes: [
      {
        id: 1,
        name: "projectName",
        slug: "projectname",
        attribute_type: "TEXT",
        options: [],
        is_required: true,
        is_translatable: true,
        order: 0,
        value: null,
      },
      {
        id: 2,
        name: "logo",
        slug: "logo",
        attribute_type: "IMAGE",
        options: [],
        is_required: true,
        is_translatable: true,
        order: 1,
        value: [],
      },
      {
        id: 3,
        name: "address",
        slug: "address",
        attribute_type: "TEXT",
        options: [],
        is_required: true,
        is_translatable: true,
        order: 2,
        value: null,
      },
      {
        id: 4,
        name: "city",
        slug: "city",
        attribute_type: "TEXT",
        options: [],
        is_required: true,
        is_translatable: true,
        order: 3,
        value: null,
      },
      {
        id: 5,
        name: "handoverDate",
        slug: "handoverdate",
        attribute_type: "DATE",
        options: [],
        is_required: true,
        is_translatable: true,
        order: 4,
        value: null,
      },
      {
        id: 6,
        name: "description",
        slug: "description",
        attribute_type: "TEXT",
        options: [],
        is_required: true,
        is_translatable: true,
        order: 5,
        value: null,
      },
      {
        id: 7,
        name: "nearbyAmenities",
        slug: "nearbyamenities",
        attribute_type: "MULTI_SELECT",
        options: [
          "Dining & Entertainmen",
          "Health Centre",
          "Kindergarten",
          "Mosque",
          "Public Park",
          "Retail Centres",
          "School",
          "Sports Ground",
        ],
        is_required: true,
        is_translatable: true,
        order: 6,
        value: null,
      },
      {
        id: 8,
        name: "paymentPlanReservationFeeRefundable",
        slug: "paymentplanreservationfeerefundable",
        attribute_type: "BOOLEAN",
        options: [],
        is_required: true,
        is_translatable: false,
        order: 7,
        value: null,
      },
      {
        id: 9,
        name: "paymentPlanFeeType",
        slug: "paymentplanfeetype",
        attribute_type: "SELECT",
        options: ["Fixed", "Percentage of Total"],
        is_required: true,
        is_translatable: false,
        order: 8,
        value: null,
      },
      {
        id: 10,
        name: "reservationFeeAmount",
        slug: "reservationfeeamount",
        attribute_type: "NUMBER",
        options: [],
        is_required: true,
        is_translatable: false,
        order: 9,
        value: null,
      },
      {
        id: 11,
        name: "paymentPlanDownPayment",
        slug: "paymentplandownpayment",
        attribute_type: "NUMBER",
        options: [],
        is_required: true,
        is_translatable: false,
        order: 10,
        value: null,
      },
      {
        id: 12,
        name: "paymentPlanHandover",
        slug: "paymentplanhandover",
        attribute_type: "NUMBER",
        options: [],
        is_required: true,
        is_translatable: false,
        order: 11,
        value: null,
      },
      {
        id: 13,
        name: "paymentPlanDuringConstruction",
        slug: "paymentplanduringconstruction",
        attribute_type: "NUMBER",
        options: [],
        is_required: true,
        is_translatable: false,
        order: 12,
        value: null,
      },
      {
        id: 19,
        name: "RegaLicenceNumber",
        slug: "regalicencenumber",
        attribute_type: "NUMBER",
        options: [],
        is_required: true,
        is_translatable: false,
        order: 13,
        value: null,
      },
    ],
    attributes_count: 20,
    product_attributes_count: 6,
    category_attributes_count: 14,
    categories_using_template: [],
    created_date: "2025-07-16T11:10:30.731498Z",
    updated_date: "2025-07-16T11:14:21.906338Z",
    marketplace_asset_categories: [
      {
        id: 1,
        name: "Brochures",
        slug: "brochures",
        description: "Brochure for the project",
        allowed_file_types: ["pdf", "jpg", "png"],
        max_file_size: 5,
        order: 0,
        is_active: true,
        assets_count: 0,
      },
      {
        id: 2,
        name: "Floor Plans",
        slug: "floor-plans",
        description: "Floor Plans",
        allowed_file_types: ["pdf", "jpg", "png"],
        max_file_size: 5,
        order: 0,
        is_active: true,
        assets_count: 0,
      },
      {
        id: 3,
        name: "Masterplan",
        slug: "masterplan",
        description: "masterplan",
        allowed_file_types: ["pdf", "jpg", "png"],
        max_file_size: 5,
        order: 0,
        is_active: true,
        assets_count: 0,
      },
    ],
  };

export default function UnitEdit() {
  return <AddProductPage defaultValues={formSchema} />;
}
