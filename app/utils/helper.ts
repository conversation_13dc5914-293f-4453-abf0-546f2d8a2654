export const formatCurrency = (value: number | string, locale: string = 'en-US', currency: string = 'USD'): string => {
    const number = typeof value === 'string' ? parseFloat(value) : value;
  
    if (isNaN(number)) return '';
  
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(number);
  }

export const formatDateToDDMMYYYY = (date: Date): string => {
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  return `${day}-${month}-${year}`;
}  
  