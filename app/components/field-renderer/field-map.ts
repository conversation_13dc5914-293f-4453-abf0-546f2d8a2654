import { RDSUploadFile } from "@roshn/ui-kit";

import { DatePicker } from "../form-components/date-picker/date-picker";
import { RichTextEditor } from "../form-components/editor/editor";
import { ButtonFileUpload } from "../form-components/file-upload/button-file-upload";
import { Input } from "../form-components/input/input";
import { RadioGroup } from "../form-components/radio-button/radio-button";
import { TagSelector } from "../form-components/tag-selector/tags-selector";
import { TextArea } from "../form-components/text-area/text-area";

export const fieldMap = {
  TEXT: Input,
  TEXTAREA: TextArea,
  MULTI_SELECT: TagSelector,
  NUMBER: Input,
  IMAGE: RDSUploadFile,
  DATE: DatePicker,
  BOOLEAN: RadioGroup,
  EDITOR: RichTextEditor,
  UPLOAD: ButtonFileUpload,
};
