import { css } from "@emotion/react";
import { AppTheme, RDSTypography } from "@roshn/ui-kit";
import { AssetWrapper } from "node_modules/@roshn/ui-kit/dist/components/rds-components/asset-wrapper";

import { createSvg } from "../svgs";

const Slash = createSvg(() => import("~/assets/icons/slash.svg"));

type BreadCrumbProps = {
  items: { label: string; action?: () => void }[];
};

const styles = {
  wrapper: css({
    display: "flex",
    gap: "2px",
  }),

  typography: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.lg,
      color: theme.rds.color.text.brand.secondary.lighter.default,
      display: "flex",
      cursor: "pointer",
    }),

  lastItem: (theme: AppTheme) =>
    css({
      color: theme.rds.color.text.brand.primary.default,
      cursor: "auto",
    }),
};

export const BreadCrumbs = ({ items }: BreadCrumbProps) => {
  return (
    <div css={styles.wrapper}>
      {items.map(({ action = () => {}, label }, idx) => {
        const isLastItem = idx === items.length - 1;

        return (
          <RDSTypography
            css={[styles.typography, isLastItem && styles.lastItem]}
            key={idx}
            onClick={action}
          >
            {label}
            {!isLastItem && (
              <AssetWrapper>
                <Slash />
              </AssetWrapper>
            )}
          </RDSTypography>
        );
      })}
    </div>
  );
};
