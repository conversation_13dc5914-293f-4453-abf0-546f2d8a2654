import { css } from "@emotion/react";
import { AppTheme, RDSTagBasic, RDSTypography } from "@roshn/ui-kit";
import { RdsTagBasicProps } from "node_modules/@roshn/ui-kit/dist/components/rds-components/TagBasic/TagBasic";

const styles = {
  wrapper: (theme: AppTheme) =>
    css({
      width: "100%",
      backgroundColor: theme.rds.color.background.ui.canvas,
      padding: theme.rds.dimension["300"],
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      border: `1px solid ${theme.rds.color.border.ui.primary}`,
    }),

  headingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h5,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary,
      textTransform: "uppercase",
    }),

  childrenWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
    }),

  divider: (theme: AppTheme) =>
    css({
      height: "1px",
      backgroundColor: theme.rds.color.border.ui.primary,
    }),

  headingWrapper: css({
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
  }),
};

export const Section = ({
  heading,
  children,
  tag,
}: {
  heading: string;
  children: React.ReactNode;
  tag?: RdsTagBasicProps;
}) => {
  return (
    <div css={styles.wrapper}>
      <div css={styles.headingWrapper}>
        <RDSTypography css={styles.headingText}>{heading}</RDSTypography>
        {tag && <RDSTagBasic size="md" {...tag} />}
      </div>
      <div css={styles.divider} />
      <div css={styles.childrenWrapper}>{children}</div>
    </div>
  );
};
