import { css } from "@emotion/react";
import { R<PERSON><PERSON><PERSON>on, RDSTypography, AppTheme, RDSAssetWrapper } from "@roshn/ui-kit";
import { ChangeEvent, useRef } from "react";
import { Controller, Control } from "react-hook-form";

import { createSvg } from "~/components/svgs";

const Trash = createSvg(() => import("~/assets/icons/trash.svg"));
const Upload = createSvg(() => import("~/assets/icons/upload.svg"));

type FileUploadProps = {
  name: string;
  control: Control;
  label?: string;
  accept?: string;
  multiple?: boolean;
  uploadDescription?: string;
  uploadExample?: string;
};

const styles = {
  emptyWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      marginBlock: theme.rds.dimension["500"],
      gap: theme.rds.dimension["200"],
      width: "100%",
      alignItems: "center",
    }),

  headingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h4,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary.primary,
    }),

  descriptionText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.body.md,
      color: theme.rds.color.text.ui.primary.secondary,
      textAlign: "center",
    }),

  uploadWrapper: css({
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  }),

  uploadInfoWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["100"],
    }),

  uploadDes: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.md,
      color: theme.rds.color.text.ui.primary,
    }),

  uploadDesExample: (theme: AppTheme) =>
    css({
      ...theme.rds.brand.font.fontFamily.label,
      ...theme.rds.font.fontSize["75"],
      color: theme.rds.color.text.ui.tertiary,
    }),

  fileList: css({
    marginTop: "16px",
    display: "flex",
    flexDirection: "column",
    gap: "8px",
  }),

  fileItem: css({
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    border: "1px solid #eee",
    borderRadius: "4px",
    padding: "8px 12px",
  }),

  removeButton: css({
    cursor: "pointer",
    width: "16px",
    height: "16px",
  }),
};

export function ButtonFileUpload({
  name,
  control,
  label = "Upload File",
  accept = "*",
  multiple = true,
  ...rest
}: FileUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { value, onChange } }) => {
        const files = multiple ? (value as File[]) || [] : value ? [value as File] : [];

        const handleRemove = (indexToRemove: number) => {
          const updatedFiles = files.filter((_, i) => i !== indexToRemove);
          onChange(multiple ? updatedFiles : null);
        };

        return (
          <div>
            <div css={styles.uploadWrapper}>
              <div css={styles.uploadInfoWrapper}>
                {rest.uploadDescription && <RDSTypography css={styles.uploadDes}>{rest.uploadDescription}</RDSTypography>}
                {rest.uploadExample && <RDSTypography css={styles.uploadDesExample}>{rest.uploadExample}</RDSTypography>}
              </div>
              <RDSButton
                type="button"
                css={{ textTransform: "none" }}
                variant="secondary"
                size="lg"
                text={label}
                data-testid="upload-button"
                onClick={handleButtonClick}
                leadIcon={
                  <RDSAssetWrapper>
                    <Upload />
                  </RDSAssetWrapper>
                }
              />
            </div>

            <input
              type="file"
              multiple={multiple}
              accept={accept}
              ref={fileInputRef}
              style={{ display: "none" }}
              onChange={(e: ChangeEvent<HTMLInputElement>) => {
                const fileList = e.target.files;
                if (fileList) {
                  onChange(multiple ? [...files, ...Array.from(fileList)] : fileList[0]);
                }
              }}
            />

            <div data-testid="file-list" css={styles.fileList}>
              {files.length > 0 ? (
                files.map((file, index) => (
                  <div key={index} css={styles.fileItem}>
                    <span>{file.name}</span>
                    <button
                      type="button"
                      css={styles.removeButton}
                      onClick={() => handleRemove(index)}
                    >
                      <Trash />
                    </button>
                  </div>
                ))
              ) : (
                <div css={styles.emptyWrapper}>
                  <RDSTypography css={styles.headingText}>No attachments yet</RDSTypography>
                  <RDSTypography css={styles.descriptionText}>
                    Upload files to share important project details — they will be organized by
                    category for easy access.
                  </RDSTypography>
                </div>
              )}
            </div>
          </div>
        );
      }}
    />
  );
}
