import { RDSUploadFile, RDSUploadFileProps } from "@roshn/ui-kit";
import { Controller, Control, FieldValues, Path } from "react-hook-form";

type ControlledUploadFileProps<T extends FieldValues> = {
  name: Path<T>;
  control: Control<T>;
} & Omit<RDSUploadFileProps, "name" | "value" | "onChange">;

export const UploadFile = <T extends FieldValues>({
  name,
  control,
  ...rest
}: ControlledUploadFileProps<T>) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState }) => {
        return (
          <RDSUploadFile
            {...rest}
            value={field.value}
            onChange={field.onChange}
            validationMsg={fieldState.error?.message}
            validationType={fieldState.error ? "error" : rest.validationType}
          />
        );
      }}
    />
  );
};
