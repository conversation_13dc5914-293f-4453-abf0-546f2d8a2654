import { css, useTheme } from "@emotion/react";
import { RDSTypography, RDSIconButton } from "@roshn/ui-kit";
import CharacterCount from "@tiptap/extension-character-count";
import { Color } from "@tiptap/extension-color";
import Link from "@tiptap/extension-link";
import ListItem from "@tiptap/extension-list-item";
import Placeholder from "@tiptap/extension-placeholder";
import TextStyle from "@tiptap/extension-text-style";
import Underline from "@tiptap/extension-underline";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { useEffect, useMemo } from "react";
import { Controller, Control } from "react-hook-form";

import { createSvg } from "~/components/svgs";
import { AppTheme } from "~/theme";

const Bold = createSvg(() => import("~/assets/icons/B.svg"));
const UnderlineIcon = createSvg(() => import("~/assets/icons/U.svg"));
const Italic = createSvg(() => import("~/assets/icons/I.svg"));
const LinkIcon = createSvg(() => import("~/assets/icons/link.svg"));
const List = createSvg(() => import("~/assets/icons/list.svg"));

type EditorProps = {
  /**
   * The current HTML content of the editor.
   * Passed in from parent (controlled component).
   */
  value: string;

  /**
   * Callback to notify parent of content changes.
   * Receives updated HTML content.
   */
  onChange: (value: string) => void;

  /**
   * Optional label displayed above the editor.
   */
  label?: string;

  /**
   * Optional helper message shown below the editor.
   */
  helperText?: string;

  /**
   * Displays a red asterisk next to label if true.
   */
  isRequired?: boolean;

  /**
   * Placeholder text shown when editor is empty.
   */
  placeholder?: string;

  /**
   * If true, shows a character counter (max 500).
   */
  counter?: boolean;

  /**
   * If true, disables editor interactions and UI.
   */
  isDisabled?: boolean;

  /**
   * Marks the field invalid for visual feedback.
   * Shows error styling on helper text.
   */
  isInvalid?: boolean;
};

const styles = {
  container: (theme: AppTheme) =>
    css({
      display: "flex",
      justifyContent: "center",
      alignItems: "flex-start",
      gap: theme?.rds?.dimension[50],
      width: "100%",
    }),

  labelContainer: (theme: AppTheme) =>
    css({
      display: "flex",
      alignItems: "center",
      gap: theme?.rds?.dimension[25],
    }),

  label: (theme: AppTheme) =>
    css({
      margin: "0",
      fontSize: theme?.rds?.font.fontSize[87],
      lineHeight: theme?.rds?.font.lineHeight[100],
      color: theme?.rds?.color.text.primary,
      "[data-disabled=true] &": {
        color: theme?.rds?.color.text.ui.disabled,
      },
    }),

  requiredSymbol: (theme: AppTheme) =>
    css({
      color: theme?.rds?.color.text.functional.danger.tertiary,
    }),

  divider: (theme: AppTheme) =>
    css({
      height: "100%",
      border: `1px solid ${theme.rds.color.border.ui.secondary}`,
      width: "1px",
      marginInline: theme.rds.dimension["200"],
    }),

  button: css({
    height: "16px",
    width: "16px",
    background: "transparent",
    "& path": {
      stroke: "none",
    },
    ":hover": {
      "& path": {
        stroke: "none",
      },
      background: "transparent",
    },
  }),

  buttonWrapper: (theme: AppTheme) =>
    css({
      padding: "3px",
      width: theme.rds.dimension["400"],
      height: theme.rds.dimension["400"],
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
    }),

  wrapper: (theme: AppTheme) =>
    css({
      border: `1px solid ${theme.rds.color.border.ui.primary}`,
      minHeight: "144px",

      // Tiptap (based on ProseMirror) editor container styles
      ".ProseMirror": {
        outline: "none",
        whiteSpace: "pre-wrap",
        overflowY: "auto",
      },

      // Placeholder styling for Tiptap when paragraph is empty
      ".ProseMirror p.is-empty:first-child::before": {
        ...theme?.rds?.typographies?.body?.sm,
        content: "attr(data-placeholder)",
        color: theme.rds.color.text.brand.secondary.lighter.default,
        minHeight: "144px",
      },
    }),

  menubar: (theme: AppTheme) =>
    css({
      height: theme.rds.dimension["500"],
      paddingInline: theme.rds.dimension["200"],
      paddingBlock: theme.rds.dimension["100"],
      display: "flex",
      gap: theme.rds.dimension["100"],
      backgroundColor: theme.rds.color.background.ui.tertiary.default,
      alignItems: "center",
    }),

  editor: (theme: AppTheme) =>
    css({
      ".ProseMirror": {
        paddingInline: theme.rds.dimension["150"],
      },
    }),

  text: (theme: AppTheme) =>
    css({
      ...theme?.rds?.typographies.label.sm,
      color: theme?.rds?.color.text.ui.tertiary,
      "[data-disabled=true] &": {
        color: theme?.rds?.color.text.ui.disabled,
      },
    }),

  helper: (theme: AppTheme) =>
    css({ display: "flex", alignItems: "center", gap: theme?.rds?.dimension[50] }),

  counter: css({
    marginInlineStart: "auto",
  }),

  errorText: (theme: AppTheme) =>
    css({
      color: theme?.rds?.color.text.functional.danger.tertiary,
    }),

  successText: (theme: AppTheme) =>
    css({
      color: theme?.rds?.color.text.functional.success.tertiary,
    }),
};

const MenuBar = ({ editor }: { editor: ReturnType<typeof useEditor> }) => {
  if (!editor) return null;

  if (!editor) {
    return null;
  }

  return (
    <div css={styles.menubar}>
      <RDSIconButton
        onClick={() => editor.chain().focus().toggleBold().run()}
        disabled={!editor.can().chain().focus().toggleBold().run()}
        variant="tertiary"
        css={styles.button}
        icon={<Bold />}
      />

      <RDSIconButton
        onClick={() => editor.chain().focus().toggleItalic().run()}
        disabled={!editor.can().chain().focus().toggleItalic().run()}
        css={styles.button}
        icon={<Italic css={styles.button} />}
      />

      <RDSIconButton
        onClick={() => editor.chain().focus().toggleUnderline().run()}
        disabled={!editor.can().chain().focus().toggleUnderline().run()}
        css={styles.button}
        icon={<UnderlineIcon />}
      />
      <div css={styles.divider} />
      <RDSIconButton
        onClick={() => editor.chain().focus().toggleBulletList().run()}
        css={styles.button}
        icon={<List />}
      />
      <RDSIconButton
        css={styles.button}
        onClick={() => {
          const isLinkActive = editor.isActive("link");

          if (isLinkActive) {
            editor.chain().focus().unsetLink().run();
          } else {
            const url = window.prompt("Enter URL");

            if (url && url.trim()) {
              editor.chain().focus().extendMarkRange("link").setLink({ href: url }).run();
            }
          }
        }}
        icon={<LinkIcon />}
      />
    </div>
  );
};

export const Editor = ({
  value,
  onChange,
  helperText,
  placeholder,
  isRequired,
  label,
  isDisabled,
  isInvalid,
}: EditorProps) => {
  const extensions = [
    Placeholder.configure({
      placeholder: placeholder || "Start typing...",
    }),
    CharacterCount.configure({
      limit: 500,
    }),
    Link.configure({
      openOnClick: false,
      autolink: true,
      linkOnPaste: true,
      HTMLAttributes: {
        rel: "noopener noreferrer",
        target: "_blank",
        class: "editor-link",
      },
    }),
    Underline,
    Color.configure({ types: [TextStyle.name, ListItem.name] }),
    TextStyle.configure({ types: [ListItem.name] }),
    StarterKit.configure({
      bulletList: { keepMarks: true, keepAttributes: false },
      orderedList: { keepMarks: true, keepAttributes: false },
    }),
  ];

  const editor = useEditor({
    content: "",
    extensions,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
  });

  useEffect(() => {
    if (editor && value !== editor.getHTML()) {
      editor.commands.setContent(value || "", false);
    }
  }, [value]);

  const theme = useTheme();

  const textStyle = useMemo(() => {
    if (typeof isInvalid !== "boolean") return undefined;
    return isInvalid ? styles.errorText : styles.successText;
  }, [isInvalid]);

  return (
    <>
      <div css={styles.labelContainer}>
        <RDSTypography css={styles.label}>{label}</RDSTypography>
        {isRequired && <span css={styles.requiredSymbol}>*</span>}
      </div>

      <div css={styles.wrapper}>
        <MenuBar editor={editor} />
        <EditorContent css={styles.editor} editor={editor} />
      </div>
      {helperText && (
        <div data-disabled={isDisabled} css={styles.container}>
          <RDSTypography
            fontName={theme?.rds?.typographies.label.sm}
            css={[styles.text, textStyle, styles.helper]}
          >
            {helperText}
          </RDSTypography>

          <RDSTypography
            css={[styles.text, textStyle, styles.counter]}
          >{`${editor?.storage?.characterCount?.characters() ?? 0}/500`}</RDSTypography>
        </div>
      )}
    </>
  );
};

type RichTextEditorProps = {
  control: Control;
  name: string;
} & Omit<EditorProps, "value" | "onChange">;

export const RichTextEditor = ({ control, name, ...rest }: RichTextEditorProps) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field, formState: { errors }, fieldState }) => {
        return (
          <Editor
            {...rest}
            value={field.value ?? ``}
            onChange={field.onChange}
            isInvalid={errors[name] && fieldState.isTouched ? true : undefined}
            counter={field?.value?.length ?? 0}
          />
        );
      }}
    />
  );
};
