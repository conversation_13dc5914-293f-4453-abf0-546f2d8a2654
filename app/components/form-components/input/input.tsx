import { RDSTextInput, RDSTextInputProps } from "@roshn/ui-kit";
import { Controller, Control, FieldValues, Path } from "react-hook-form";

type Direction = "ltr" | "rtl";

type ControlledTextInputProps<T extends FieldValues> = {
  name: Path<T>;
  control: Control<T>;
  label?: string;
  placeholder?: string;
  type?: string;
  dir?: Direction;
} & Omit<RDSTextInputProps, "name" | "value" | "onChange" | "onBlur">;

export function Input<T extends FieldValues>({
  name,
  control,
  label,
  placeholder,
  type = "text",
  helperText,
  dir,
  ...rest
}: ControlledTextInputProps<T>) {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState }) => (
        <div dir={dir}>
          <RDSTextInput
            {...field}
            label={label}
            placeholder={placeholder}
            type={type}
            helperText={fieldState.error?.message ?? helperText}
            isInvalid={!!fieldState.error}
            {...rest}
          />
        </div>
      )}
    />
  );
}
