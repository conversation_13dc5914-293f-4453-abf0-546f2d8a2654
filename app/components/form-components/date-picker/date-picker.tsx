import { RDSDatePicker } from "@roshn/ui-kit";
import { Controller, Control, FieldValues, Path } from "react-hook-form";

type ControlledDateRangePickerProps<T extends FieldValues> = {
  name: Path<T>;
  startDate: Date;
  control: Control<T>;
  dateInputProps?: {
    helperText: string;
  };
};

export function DatePicker<T extends FieldValues>({
  name,
  control,
  startDate,
  ...rest
}: ControlledDateRangePickerProps<T>) {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState }) => (
        <RDSDatePicker
          {...rest}
          {...field}
          selected={field.value}
          dateInputProps={{
            helperText: (fieldState?.error ?? rest?.dateInputProps?.helperText) as string,
            ...rest?.dateInputProps,
          }}
          startDate={startDate}
          onChange={(date) => {
            // Convert Date object to ISO string for form validation
            field.onChange(date);
          }}
        />
      )}
    />
  );
}
