import { css, useTheme } from "@emotion/react";
import { useNavigate, useParams } from "@remix-run/react";
import {
  AppTheme,
  RDS<PERSON>setWrap<PERSON>,
  RDSButton,
  RDSTagBasic,
  RDSModal,
  RDSTypography,
  RDSTextInput,
  RDSSwitch,
} from "@roshn/ui-kit";
import { useState } from "react";

import Logo from "~/assets/logos/beyond-al-nakheel.svg?url";
import { BreadCrumbs } from "~/components/bread-crumbs/bread-crumbs";
import { Section } from "~/components/section/section";
import { createSvg } from "~/components/svgs";
import UnitListSection from "~/features/unit-list/unit-list-section";
import { useAppPath } from "~/hooks/use-app-path";
import { AppPaths } from "~/utils/app-paths";

const Tree = createSvg(() => import("~/assets/icons/tree.svg"));
const Mosque = createSvg(() => import("~/assets/icons/mekka.svg"));
const Hospital = createSvg(() => import("~/assets/icons/hospital-building.svg"));
const Graduate = createSvg(() => import("~/assets/icons/graduate.svg"));
const File = createSvg(() => import("~/assets/icons/file.svg"));
const Image = createSvg(() => import("~/assets/icons/image-gallery.svg"));

const styles = {
  divider: (theme: AppTheme) =>
    css({
      height: "24px",
      border: `1px solid ${theme.rds.color.border.ui.secondary}`,
    }),

  tagsWrapper: css({ display: "flex", flexWrap: "wrap", gap: "16px" }),

  wrapper: (theme: AppTheme) =>
    css({
      paddingInline: theme.rds.dimension["800"],
      paddingBlock: theme.rds.dimension["400"],
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
      alignItems: "flex-start",
      backgroundColor: theme.rds.color.background.ui.secondary.default,
      minHeight: "100vh",
    }),

  additionalItemsWrapper: css({
    display: "flex",
    gap: "2rem",
  }),

  additionalContent: css({
    display: "flex",
    flexDirection: "column",
    gap: "2rem",
    textWrap: "nowrap",
  }),

  attachments: css({ display: "flex", alignItems: "center", gap: "16px" }),

  attachmentItem: css({
    display: "flex",
    gap: "8px",
    justifyContent: "center",
    alignItems: "center",
  }),

  attachmentsTypo: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.md,
      ...theme.rds.brand.font.fontFamily.label,
      ...theme.rds.font.fontSize["87"],
      fontWeight: 300,
      color: theme.rds.color.text.brand.primary.default,
    }),

  button: (theme: AppTheme) =>
    css({
      textTransform: "none",
      width: "fit-content",
      paddingInlineStart: 0,

      "& svg": {
        color: `${theme.rds.color.text.brand.primary.default} !important`,
      },
    }),

  detailWrapper: (theme: AppTheme) =>
    css({
      width: "100%",
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
    }),

  itemsWrapper: (theme: AppTheme) =>
    css({
      width: "100%",
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
    }),

  detailHeading: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.display.d5,
      ...theme.rds.brand.font.fontFamily.display,
      fontWeight: 300,
      color: theme.rds.color.text.ui.primary,
    }),

  detailSubHeading: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.body.md5,
      ...theme.rds.brand.font.fontFamily.body,
      color: theme.rds.color.text.ui.primary,
    }),

  license: (theme: AppTheme) =>
    css({
      ...theme.rds.brand.font.fontFamily.label,
      ...theme.rds.font.fontSize["100"],
      textDecoration: "underline",
    }),

  expectedStyles: (theme: AppTheme) =>
    css({
      color: theme.rds.color.text.ui.tertiary,
    }),

  detailDescription: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.body.lg,
      fontWeight: 400,
      color: theme.rds.color.text.ui.primary,
    }),

  actionsHeadingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.body.md,
      fontWeight: 500,
      color: theme.rds.color.text.ui.tertiary,
    }),

  internalWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      zIndex: 0,
    }),

  detailSubHeadingWrapper: css({
    display: "flex",
    justifyContent: "space-between",
  }),

  infoHead: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.lg,
      color: theme.rds.color.text.ui.tertiary,
    }),

  infoDes: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.emphasis.lg,
      color: theme.rds.color.text.ui.primary,
    }),

  sectionsWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["400"],
      height: "fit-content",
      width: "100%",
    }),

  infoTypoWrapper: css({
    display: "flex",
    justifyContent: "space-between",
  }),

  sectionLayout: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
    }),

  infoSections: css({
    flex: "0 0 30%",
  }),

  additionalDetailHeading: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h4,
      ...theme.rds.font.fontSize["150"],
      color: theme.rds.color.text.ui.primary,
      whiteSpace: "nowrap",
    }),

  modalDimension: css({
    "& div": {
      "& div": {
        maxWidth: "640px",
      },
    },
    "& p": {
      margin: 0,
    },
  }),

  additionalDetailContainer: (theme: AppTheme) =>
    css({
      display: "flex",
      alignItems: "center",
      gap: theme.rds.dimension["200"],
      width: "100%",
    }),

  dividerLine: (theme: AppTheme) =>
    css({
      flex: 1,
      height: "1px",
      backgroundColor: theme.rds.color.border.ui.tertiary,
    }),

  image: css({
    width: "60px",
    height: "60px",
  }),
};

const config = {
  product1: {
    community: "Beyond Al-Nakheel",
    expectedDelivery: "01/09/2029",
    description: `Beyond is a visionary real estate development company based in Riyadh, Saudi Arabia, specializing in the creation of innovative, sustainable, and community-focused residential projects across the Kingdom. From design and construction to property management and investment services, Beyond is redefining the future of living in Saudi Arabia.
Guided by the principles of quality, affordability, and long-term value, Beyond is committed to building more than just homes—it builds opportunities for better living. Each project blends modern architecture with cultural authenticity, supporting the social and environmental goals of Saudi Vision 2030.
      `,
    license: "305",
    locality: "Al-Ahsa, Eastern Region",
  },
};

const tagOptions = [
  {
    label: "Public Park",
    value: "park",
    disabled: false,
    leadIcon: <Tree />,
  },
  {
    label: "Mosque",
    value: "mosque",
    disabled: false,
    leadIcon: <Mosque />,
  },
  {
    label: "Health center",
    value: "health-center",
    disabled: false,
    leadIcon: <Hospital />,
  },

  {
    label: "Sports ground",
    value: "sport-ground",
    disabled: false,
    leadIcon: <Tree />,
  },
  {
    label: "School",
    value: "school",
    disabled: false,
    leadIcon: <Graduate />,
  },
  {
    label: "Kindergarten",
    value: "kindergarten",
    disabled: false,
    leadIcon: <Tree />,
  },
  {
    label: "Retail center",
    value: "retail-center",
    disabled: false,
    leadIcon: <Tree />,
  },
];

export default function ProjectDetail() {
  const navigate = useNavigate();
  const generateAppPath = useAppPath();
  const { id } = useParams();
  const theme = useTheme();

  const [showDeletion, setDeletion] = useState(false);
  const [delModalInput, setDelModalInp] = useState("");
  const [visible, setVisible] = useState(false);

  const details = config.product1;

  const handleProjectNav = () => {
    navigate(generateAppPath(AppPaths.project));
  };

  const handleProjectEditNav = () => {
    navigate(
      generateAppPath(AppPaths.editProject, {
        id,
      }),
    );
  };

  return (
    <div css={styles.wrapper}>
      <div css={styles.modalDimension}>
        <RDSModal
          headerProps={{
            label: "Confirm project deletion",
            type: "centred",
          }}
          isOpen={showDeletion}
          buttonsGroup={{
            buttons: [
              <RDSButton
                variant="primary"
                onClick={() => {
                  setDeletion(false);
                }}
                text="DELETE PROJECT"
                key="delete"
                disabled={delModalInput !== "DELETE"}
              />,
              <RDSButton
                variant="secondary"
                onClick={() => setDeletion(false)}
                text="CANCEL"
                key="cancel"
              />,
            ],
            direction: "vertical",
          }}
          content={
            <RDSTextInput
              value={delModalInput}
              onChange={(e) => setDelModalInp(e.target.value)}
              placeholder="DELETE"
            />
          }
          description={
            (
              <>
                <RDSTypography fontName={theme?.rds?.typographies.body.md}>
                  Deleting this project is permanent. We recommend saving it as a draft if you may
                  need it later.
                </RDSTypography>
                <RDSTypography fontName={theme?.rds?.typographies.body.md}>
                  To confirm deletion, please type DELETE in the box below.
                </RDSTypography>
              </>
            ) as any
          }
          showContent
          showDescription
        />
      </div>
      <RDSButton
        css={styles.button}
        variant="tertiary"
        size="lg"
        text="Back to project list"
        leadIcon="left_arrow"
        onClick={handleProjectNav}
      />
      <div css={styles.sectionsWrapper}>
        <div css={styles.detailWrapper}>
          <div css={styles.itemsWrapper}>
            <BreadCrumbs
              items={[
                { label: "Project list", action: handleProjectNav },
                { label: "Project details" },
              ]}
            />
            <div css={styles.detailSubHeadingWrapper}>
              <RDSTypography css={styles.detailHeading}>{details.community}</RDSTypography>
              <img src={Logo} css={styles.image} alt="Roshn Logo" />
            </div>
            <div css={styles.detailSubHeadingWrapper}>
              <RDSTypography css={styles.detailSubHeading}>
                {`${details.locality}`}
                {
                  <span css={styles.expectedStyles}>
                    (Expected for: {details.expectedDelivery})
                  </span>
                }
              </RDSTypography>
              <RDSTypography css={[styles.detailSubHeading, styles.license]}>
                {`REGA license: ${details.license}`}
              </RDSTypography>
            </div>
            <RDSTypography css={styles.detailDescription}>{details.description}</RDSTypography>
          </div>
          <div css={styles.itemsWrapper}>
            <div css={styles.additionalDetailContainer}>
              <RDSTypography css={styles.additionalDetailHeading}>Additional details</RDSTypography>
              <div css={styles.dividerLine} />
            </div>
            <div css={styles.additionalItemsWrapper}>
              <div css={styles.additionalContent}>
                <RDSTypography>Attachments:</RDSTypography>
                <RDSTypography>Nearby amenities:</RDSTypography>
              </div>
              <div css={styles.additionalContent}>
                <div css={styles.attachments}>
                  <div css={styles.attachmentItem}>
                    <RDSAssetWrapper>
                      <File />
                    </RDSAssetWrapper>
                    <RDSTypography css={styles.attachmentsTypo}>Brochure</RDSTypography>
                  </div>
                  <div css={styles.divider} />
                  <div css={styles.attachmentItem}>
                    <RDSAssetWrapper>
                      <Image />
                    </RDSAssetWrapper>
                    <RDSTypography css={styles.attachmentsTypo}>Masterplane</RDSTypography>
                  </div>
                  <div css={styles.divider} />
                  <div css={styles.attachmentItem}>
                    <RDSAssetWrapper>
                      <Image />
                    </RDSAssetWrapper>
                    <RDSTypography css={styles.attachmentsTypo}>Floor plans</RDSTypography>
                  </div>
                </div>
                <div css={styles.tagsWrapper}>
                  {tagOptions.map(({ leadIcon, label }, index) => (
                    <RDSTagBasic
                      leadIcon={leadIcon}
                      key={index}
                      label={label}
                      appearance="neutral"
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div css={[styles.sectionLayout, styles.infoSections]}>
          <Section
            heading="VISIBILITY"
            tag={{
              label: visible ? "Visible" : "Hidden",
              appearance: visible ? "success" : "neutral",
            }}
          >
            <RDSTypography css={styles.actionsHeadingText}>
              The current approved version remains visible to customers until new changes are
              approved.
            </RDSTypography>

            <div css={styles.infoTypoWrapper}>
              <RDSTypography css={styles.infoHead}>Approved on</RDSTypography>
              <RDSTypography css={styles.infoDes}>Jun 20, 2025, 14:35</RDSTypography>
            </div>

            <div css={styles.infoTypoWrapper}>
              <RDSTypography fontName={theme?.rds?.typographies.label.md}>
                Show to Customers
              </RDSTypography>
              <RDSSwitch checked={visible} onCheckedChange={setVisible} />
            </div>
          </Section>
          <Section
            heading="LISTING STATUS"
            tag={{
              label: "Daft",
              appearance: "info",
            }}
          >
            <div css={styles.internalWrapper}>
              <RDSTypography css={styles.actionsHeadingText}>
                Please complete all required fields before submitting for review.
              </RDSTypography>
              <RDSButton
                variant="primary"
                size="lg"
                text="CONTINUE EDITING"
                onClick={handleProjectEditNav}
              />
            </div>
          </Section>
          <Section heading="DELETE PROJECT">
            <div css={styles.internalWrapper}>
              <RDSTypography css={styles.actionsHeadingText}>
                Deleting this project is permanent. Consider hiding it from public visibility
                instead.
              </RDSTypography>
              <RDSButton
                variant="secondary"
                size="lg"
                text="DELETE PROJECT"
                onClick={() => setDeletion(true)}
              />
            </div>
          </Section>
        </div>
      </div>
      <UnitListSection />
    </div>
  );
}
